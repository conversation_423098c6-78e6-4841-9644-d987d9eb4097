@import "tailwindcss";
@import "./typography.css";
@custom-variant dark (&:where([data-theme=dark], [data-theme=dark] *));

:root,
html[data-theme="light"] {
  --background: #F0F2F5;
  --foreground: #1F2937;
  --accent: #007BFF;
  --muted: #E5E7EB;
  --border: #D1D5DB;
}

html[data-theme="dark"] {
  --background: #1A1B26;
  --foreground: #C0CAF5;
  --accent: #00E5FF;
  --muted: #414868;
  --border: #2A2F4F;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-accent: var(--accent);
  --color-muted: var(--muted);
  --color-border: var(--border);
}

@layer base {
  * {
    @apply border-border outline-accent/75;
    scrollbar-width: auto;
    scrollbar-color: var(--color-muted) transparent;
  }
  html {
    @apply overflow-y-scroll scroll-smooth;
  }
  body {
    @apply flex min-h-svh flex-col bg-background font-mono text-foreground selection:bg-accent/75 selection:text-background;
    padding-top: 80px; /* Add space for fixed header */
  }
  a,
  button {
    @apply outline-offset-1 outline-accent focus-visible:no-underline focus-visible:outline-2 focus-visible:outline-dashed;
  }
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
  section,
  footer {
    @apply mx-auto max-w-3xl px-4;
  }
}

.active-nav {
  @apply underline decoration-wavy decoration-2 underline-offset-4;
}

/* Source: https://piccalil.li/blog/a-more-modern-css-reset/ */
/* Anything that has been anchored to should have extra scroll margin */
:target {
  scroll-margin-block: 6rem;
}
